### Deployment
[Deployment at PythonAnywhere](https://eduardomsilveira.pythonanywhere.com/)

### Endpoints
| Method | Path                    | Description                 |
| ------ | ----------------------- | --------------------------- |
| GET    | `/users`                | List all users              |
| POST   | `/user`                 | Create user                 |
| GET    | `/user/<id>`            | Get user by ID              |
| DELETE | `/user/<id>`            | Delete user by ID           |
| GET    | `/user/<id>/components` | List user’s components      |
| GET    | `/components`           | List all components         |
| POST   | `/component`            | Create and link a component |
| GET    | `/component/<id>`       | Get component by ID         |
| PUT    | `/component/<id>`       | Full update of a component  |
| DELETE | `/component/<id>`       | Delete component by ID      |
